{".class": "MypyFile", "_fullname": "pdf_generator", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "A4": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "pdf_generator.A4", "name": "A4", "type": {".class": "AnyType", "missing_import_name": "pdf_generator.A4", "source_any": null, "type_of_any": 3}}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BytesIO": {".class": "SymbolTableNode", "cross_ref": "_io.BytesIO", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PDFGenerator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pdf_generator.PDFGenerator", "name": "PDFGenerator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pdf_generator.PDFGenerator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pdf_generator", "mro": ["pdf_generator.PDFGenerator", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pdf_generator.PDFGenerator.__init__", "name": "__init__", "type": null}}, "_add_ai_processing_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "story", "document_info"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pdf_generator.PDFGenerator._add_ai_processing_info", "name": "_add_ai_processing_info", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "story", "document_info"], "arg_types": ["pdf_generator.PDFGenerator", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_ai_processing_info of PDFGenerator", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_add_content": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "story", "content"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pdf_generator.PDFGenerator._add_content", "name": "_add_content", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "story", "content"], "arg_types": ["pdf_generator.PDFGenerator", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_content of PDFGenerator", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_add_diff_section": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "story", "diff_content"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pdf_generator.PDFGenerator._add_diff_section", "name": "_add_diff_section", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "story", "diff_content"], "arg_types": ["pdf_generator.PDFGenerator", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_diff_section of PDFGenerator", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_add_footer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "story"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pdf_generator.PDFGenerator._add_footer", "name": "_add_footer", "type": null}}, "_add_header": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "story", "document_info"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pdf_generator.PDFGenerator._add_header", "name": "_add_header", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "story", "document_info"], "arg_types": ["pdf_generator.PDFGenerator", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_header of PDFGenerator", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_add_paragraph": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "story", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pdf_generator.PDFGenerator._add_paragraph", "name": "_add_paragraph", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "story", "text"], "arg_types": ["pdf_generator.PDFGenerator", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_paragraph of PDFGenerator", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_add_user_documentation_input": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "story", "document_info"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pdf_generator.PDFGenerator._add_user_documentation_input", "name": "_add_user_documentation_input", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "story", "document_info"], "arg_types": ["pdf_generator.PDFGenerator", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_user_documentation_input of PDFGenerator", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_add_user_feedback_review": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "story", "document_info"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pdf_generator.PDFGenerator._add_user_feedback_review", "name": "_add_user_feedback_review", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "story", "document_info"], "arg_types": ["pdf_generator.PDFGenerator", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_user_feedback_review of PDFGenerator", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_setup_custom_styles": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pdf_generator.PDFGenerator._setup_custom_styles", "name": "_setup_custom_styles", "type": null}}, "generate_document_pdf": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "content", "diff_content", "document_info", "document"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pdf_generator.PDFGenerator.generate_document_pdf", "name": "generate_document_pdf", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "content", "diff_content", "document_info", "document"], "arg_types": ["pdf_generator.PDFGenerator", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_document_pdf of PDFGenerator", "ret_type": {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_available": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pdf_generator.PDFGenerator.is_available", "name": "is_available", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pdf_generator.PDFGenerator"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_available of PDFGenerator", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "logger": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pdf_generator.PDFGenerator.logger", "name": "logger", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "styles": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pdf_generator.PDFGenerator.styles", "name": "styles", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pdf_generator.PDFGenerator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pdf_generator.PDFGenerator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PDFGeneratorFallback": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pdf_generator.PDFGeneratorFallback", "name": "PDFGeneratorFallback", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pdf_generator.PDFGeneratorFallback", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pdf_generator", "mro": ["pdf_generator.PDFGeneratorFallback", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pdf_generator.PDFGeneratorFallback.__init__", "name": "__init__", "type": null}}, "generate_document_pdf": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pdf_generator.PDFGeneratorFallback.generate_document_pdf", "name": "generate_document_pdf", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["pdf_generator.PDFGeneratorFallback", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_document_pdf of PDFGeneratorFallback", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_available": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pdf_generator.PDFGeneratorFallback.is_available", "name": "is_available", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pdf_generator.PDFGeneratorFallback"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_available of PDFGeneratorFallback", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "logger": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pdf_generator.PDFGeneratorFallback.logger", "name": "logger", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pdf_generator.PDFGeneratorFallback.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pdf_generator.PDFGeneratorFallback", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Paragraph": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "pdf_generator.Paragraph", "name": "Paragraph", "type": {".class": "AnyType", "missing_import_name": "pdf_generator.Paragraph", "source_any": null, "type_of_any": 3}}}, "ParagraphStyle": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "pdf_generator.ParagraphStyle", "name": "ParagraphStyle", "type": {".class": "AnyType", "missing_import_name": "pdf_generator.ParagraphStyle", "source_any": null, "type_of_any": 3}}}, "Preformatted": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "pdf_generator.Preformatted", "name": "Preformatted", "type": {".class": "AnyType", "missing_import_name": "pdf_generator.Preformatted", "source_any": null, "type_of_any": 3}}}, "REPORTLAB_AVAILABLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "pdf_generator.REPORTLAB_AVAILABLE", "name": "REPORTLAB_AVAILABLE", "type": "builtins.bool"}}, "SimpleDocTemplate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "pdf_generator.SimpleDocTemplate", "name": "SimpleDocTemplate", "type": {".class": "AnyType", "missing_import_name": "pdf_generator.SimpleDocTemplate", "source_any": null, "type_of_any": 3}}}, "Spacer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "pdf_generator.Spacer", "name": "Spacer", "type": {".class": "AnyType", "missing_import_name": "pdf_generator.Spacer", "source_any": null, "type_of_any": 3}}}, "TA_CENTER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "pdf_generator.TA_CENTER", "name": "TA_CENTER", "type": {".class": "AnyType", "missing_import_name": "pdf_generator.TA_CENTER", "source_any": null, "type_of_any": 3}}}, "TA_LEFT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "pdf_generator.TA_LEFT", "name": "TA_LEFT", "type": {".class": "AnyType", "missing_import_name": "pdf_generator.TA_LEFT", "source_any": null, "type_of_any": 3}}}, "TA_RIGHT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "pdf_generator.TA_RIGHT", "name": "TA_RIGHT", "type": {".class": "AnyType", "missing_import_name": "pdf_generator.TA_RIGHT", "source_any": null, "type_of_any": 3}}}, "Table": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "pdf_generator.Table", "name": "Table", "type": {".class": "AnyType", "missing_import_name": "pdf_generator.Table", "source_any": null, "type_of_any": 3}}}, "TableStyle": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "pdf_generator.TableStyle", "name": "TableStyle", "type": {".class": "AnyType", "missing_import_name": "pdf_generator.TableStyle", "source_any": null, "type_of_any": 3}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pdf_generator.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pdf_generator.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pdf_generator.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pdf_generator.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pdf_generator.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pdf_generator.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "black": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "pdf_generator.black", "name": "black", "type": {".class": "AnyType", "missing_import_name": "pdf_generator.black", "source_any": null, "type_of_any": 3}}}, "blue": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "pdf_generator.blue", "name": "blue", "type": {".class": "AnyType", "missing_import_name": "pdf_generator.blue", "source_any": null, "type_of_any": 3}}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "getSampleStyleSheet": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "pdf_generator.getSampleStyleSheet", "name": "getSampleStyleSheet", "type": {".class": "AnyType", "missing_import_name": "pdf_generator.getSampleStyleSheet", "source_any": null, "type_of_any": 3}}}, "gray": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "pdf_generator.gray", "name": "gray", "type": {".class": "AnyType", "missing_import_name": "pdf_generator.gray", "source_any": null, "type_of_any": 3}}}, "green": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "pdf_generator.green", "name": "green", "type": {".class": "AnyType", "missing_import_name": "pdf_generator.green", "source_any": null, "type_of_any": 3}}}, "inch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "pdf_generator.inch", "name": "inch", "type": {".class": "AnyType", "missing_import_name": "pdf_generator.inch", "source_any": null, "type_of_any": 3}}}, "letter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "pdf_generator.letter", "name": "letter", "type": {".class": "AnyType", "missing_import_name": "pdf_generator.letter", "source_any": null, "type_of_any": 3}}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "red": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "pdf_generator.red", "name": "red", "type": {".class": "AnyType", "missing_import_name": "pdf_generator.red", "source_any": null, "type_of_any": 3}}}}, "path": "C:\\home-repos\\reposense_ai\\pdf_generator.py"}