{"data_mtime": 1754498338, "dep_lines": [7, 8, 9, 10, 11, 12, 14, 15, 16, 17, 18, 19, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["logging", "sys", "threading", "time", "datetime", "pathlib", "config_manager", "email_service", "file_manager", "models", "ollama_client", "repository_backends", "builtins", "_frozen_importlib", "_typeshed", "abc", "os", "repository_backends.base", "typing", "typing_extensions"], "hash": "eecec8134c8ea28a795d243efffc17c63edff036", "id": "monitor_service", "ignore_all": true, "interface_hash": "51d8ab1d2c75213759ce06a0cb417769d0be6651", "mtime": 1754498704, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\reposense_ai\\monitor_service.py", "plugin_data": null, "size": 13772, "suppressed": [], "version_id": "1.15.0"}