{"data_mtime": 1754498338, "dep_lines": [9, 10, 7, 8, 12, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["email.mime.text", "email.mime.multipart", "logging", "smtplib", "models", "builtins", "_frozen_importlib", "_ssl", "abc", "email", "email._policybase", "email.message", "email.mime", "email.mime.base", "email.mime.nonmultipart", "ssl", "types", "typing", "typing_extensions"], "hash": "9ab7da61c4eda44da1c7bfa41b78d4085ce2c0bd", "id": "email_service", "ignore_all": true, "interface_hash": "55923a0e588c4c74d0b534ffa76a80ba4fd7d397", "mtime": 1754076664, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\reposense_ai\\email_service.py", "plugin_data": null, "size": 2384, "suppressed": [], "version_id": "1.15.0"}