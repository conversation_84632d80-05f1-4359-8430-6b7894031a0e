{"data_mtime": 1754678935, "dep_lines": [6, 7, 8, 9, 320, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["logging", "subprocess", "typing", "document_database", "re", "builtins", "_frozen_importlib", "_typeshed", "abc", "enum", "os", "typing_extensions"], "hash": "0a0acea512c57987fd4be2c1e163eb5a36e22f00", "id": "diff_service", "ignore_all": false, "interface_hash": "77836381709bfb80f2e83d7951e2f5910110fef0", "mtime": 1754678934, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\reposense_ai\\diff_service.py", "plugin_data": null, "size": 17577, "suppressed": [], "version_id": "1.15.0"}