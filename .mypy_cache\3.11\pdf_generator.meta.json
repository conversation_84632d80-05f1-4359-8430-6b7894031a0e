{"data_mtime": 1754528413, "dep_lines": [8, 9, 10, 11, 624, 1, 1, 1, 14, 15, 16, 17, 20, 18], "dep_prios": [10, 5, 5, 5, 20, 5, 30, 30, 5, 5, 5, 5, 5, 5], "dependencies": ["logging", "datetime", "typing", "io", "re", "builtins", "_frozen_importlib", "abc"], "hash": "00215ce1cb4ae35851046b52bcd9f6dab90b6e7f", "id": "pdf_generator", "ignore_all": true, "interface_hash": "992d7bbf46553d4036e5b8e6dbaf6460d83786c4", "mtime": 1754520395, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\reposense_ai\\pdf_generator.py", "plugin_data": null, "size": 27027, "suppressed": ["reportlab.lib.pagesizes", "reportlab.lib.styles", "reportlab.lib.units", "reportlab.lib.colors", "reportlab.lib.enums", "reportlab.platypus"], "version_id": "1.15.0"}